import { Injectable } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router, ActivatedRoute } from '@angular/router';
import { BehaviorSubject, Observable, catchError, throwError } from 'rxjs';

// Services
import { AuthService } from '../../../core/services/auth.service';
import { ToastService } from '../../../core/services/toast.service';
import { NotifyType } from '../../../constant/notify-type';
import { passwordValidator } from '../../../core/common/password-validator';

// Models
import { UserReq } from '../../../model/request/user-req.model';

export interface SignUpState {
  registrationForm: FormGroup;
  isLoading: boolean;
  registrationError: string;
}

@Injectable({
  providedIn: 'root'
})
export class SignUpLogicService {
  private stateSubject = new BehaviorSubject<SignUpState>({
    registrationForm: this.createForm(),
    isLoading: false,
    registrationError: ''
  });

  public state$ = this.stateSubject.asObservable();

  constructor(
    private fb: FormBuilder,
    private authService: AuthService,
    private router: Router,
    private toastService: ToastService,
    private activatedRoute: ActivatedRoute
  ) {
    this.initializeReferralCode();
  }

  private createForm(): FormGroup {
    return this.fb.group({
      email: ['', [Validators.required, Validators.email]],
      username: ['', Validators.required],
      password: ['', [Validators.required, Validators.minLength(8), passwordValidator()]],
      confirmPassword: ['', Validators.required],
      termsAgreed: [false, Validators.requiredTrue],
      referralCode: [''] // Optional field for referral code
    }, {
      validators: this.passwordMatchValidator
    });
  }

  private passwordMatchValidator(form: FormGroup) {
    const password = form.get('password');
    const confirmPassword = form.get('confirmPassword');
    
    if (password && confirmPassword && password.value !== confirmPassword.value) {
      confirmPassword.setErrors({ passwordMismatch: true });
      return { passwordMismatch: true };
    }
    
    if (confirmPassword?.hasError('passwordMismatch')) {
      delete confirmPassword.errors?.['passwordMismatch'];
      if (Object.keys(confirmPassword.errors || {}).length === 0) {
        confirmPassword.setErrors(null);
      }
    }
    
    return null;
  }

  private initializeReferralCode(): void {
    // Check for referral code in URL parameters
    this.activatedRoute.queryParams.subscribe(params => {
      const referralCode = params['ref'];
      if (referralCode) {
        const currentState = this.stateSubject.value;
        currentState.registrationForm.patchValue({
          referralCode: referralCode
        });
        console.log('Referral code detected from URL:', referralCode);
      }
    });
  }

  public onSubmit(): void {
    const currentState = this.stateSubject.value;
    
    if (currentState.registrationForm.valid) {
      this.updateState({ isLoading: true, registrationError: '' });

      // Map form values to UserReq model
      const userReq: UserReq = {
        user_name: currentState.registrationForm.get('username')?.value,
        email: currentState.registrationForm.get('email')?.value,
        password: currentState.registrationForm.get('password')?.value,
        name: '', // Not collected in the form anymore
        phone: '', // Not collected in the form, but required by the API model
        referral_code: currentState.registrationForm.get('referralCode')?.value || undefined
      };

      console.log('Registering user:', userReq);

      this.authService.register(userReq)
        .pipe(
          catchError(error => {
            this.updateState({ isLoading: false });
            console.error('Registration error:', error);
            let errorMessage = 'Registration failed. Please try again.';
            
            if (error && error.message) {
              errorMessage = error.message;
            } else if (error && error.error && error.error.message) {
              errorMessage = error.error.message;
            }
            
            this.updateState({ registrationError: errorMessage });
            this.toastService.showToast(errorMessage, NotifyType.ERROR);
            return throwError(() => error);
          })
        )
        .subscribe({
          next: (user) => {
            console.log('Registration successful:', user);
            this.toastService.showToast('Registration successful!', NotifyType.SUCCESS);
            // Navigate to dashboard
            this.router.navigate(['/dashboard']).then(() => {
              window.location.reload();
            });
          },
          error: (error) => {
            
            console.error('Registration subscription error:', error);
            // Error is already handled in the catchError operator
          },
          complete: () => {
            this.updateState({ isLoading: false });
          }
        });
    } else {
      // Mark all form controls as touched to show validation errors
      Object.keys(currentState.registrationForm.controls).forEach(key => {
        const control = currentState.registrationForm.get(key);
        control?.markAsTouched();
      });
    }
  }

  public login(): void {
    this.router.navigate(['/auth/login']);
  }

  private updateState(partialState: Partial<SignUpState>): void {
    const currentState = this.stateSubject.value;
    this.stateSubject.next({
      ...currentState,
      ...partialState
    });
  }

  // Getters for form controls
  public get email() {
    return this.stateSubject.value.registrationForm.get('email');
  }

  public get username() {
    return this.stateSubject.value.registrationForm.get('username');
  }

  public get password() {
    return this.stateSubject.value.registrationForm.get('password');
  }

  public get confirmPassword() {
    return this.stateSubject.value.registrationForm.get('confirmPassword');
  }

  public get termsAgreed() {
    return this.stateSubject.value.registrationForm.get('termsAgreed');
  }

  public get referralCode() {
    return this.stateSubject.value.registrationForm.get('referralCode');
  }
}
