import { GeneralSvRes } from "./general-sv.model";
import { ProviderRes } from "./provider-res.model";
import { ServiceLabel } from "./service-label.model";



export class SpecialPriceLiteRes {
  id: number = 0;
  discount_type: string = 'FIXED';
  discount_value: number = 0;

}


/**
 * Model for service response with default values
 */
export class SuperGeneralSvRes implements GeneralSvRes {
  // GeneralSvRes properties
  id: number = 0;
  name: string = '';
  description: string = '';
  price: number = 0;
  add_type: string = 'MANUAL';
  type: string = '';
  min: number = 1000;
  max: number = 5000;
  icon: string = '';
  hide: boolean = false;
  is_favorite: boolean = false;
  sort: number = 0;
  category_id: number = 0;
  sample_link: string = '';

  // SuperGeneralSvRes specific properties
  price1: number = 0;
  price2: number = 0;
  original_price: number = 0;
  api_service_id: string = '';
  api_provider: ProviderRes = {
    id: 0,
    url: '',
    secret_key: '',
    balance: 0,
    balance_alert: '',
    preview_balance: 0,
    currency: '',
    currency_rate: 1,
    enable_rate: false,
    status: '',
    name: '',
    isLoading: false
  };
  status: string = 'ACTIVATED';
  price_label?: string = '';
  auto_sync: boolean = false;
  limit_from: number = 0;
  limit_to: number = 0;

  // Sync settings
  sync_min_max: boolean = false;
  sync_refill: boolean = false;
  sync_cancel: boolean = false;
  sync_status: boolean = false;
  labels: ServiceLabel[] = [];
  refill: boolean = false;
  refill_days?: number = 30;
  percent: number = 0;
  percent1: number = 0;
  percent2: number = 0;
  average_time: number = 0;
  is_overflow: boolean = false;
  overflow: number = 0;
  is_fixed_price: boolean = false;
  speed_per_day: number = 0;

  // User-friendly description fields
  start_time_content: string = '';
  start_time_enabled: boolean = false;
  warranty_content: string = '';
  warranty_enabled: boolean = false;
  speed_content: string = '';
  speed_enabled: boolean = false;

  cancel_button: boolean = false;
  special_prices: SpecialPriceLiteRes[] = [];

  /**
   * Creates a new SuperGeneralSvRes with default values
   * @returns A new SuperGeneralSvRes instance with default values
   */
  static createDefault(): SuperGeneralSvRes {
    return new SuperGeneralSvRes();
  }

  /**
   * Creates a new SuperGeneralSvRes with custom values
   * @param data Partial data to override default values
   * @returns A new SuperGeneralSvRes instance with custom values
   */
  static create(data: Partial<SuperGeneralSvRes>): SuperGeneralSvRes {
    return Object.assign(new SuperGeneralSvRes(), data);
  }
}
