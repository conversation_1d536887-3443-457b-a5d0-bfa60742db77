import { CommonModule } from '@angular/common';
import { Component, OnInit, OnDestroy } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { AuthService } from '../../../core/services/auth.service';
import { Router, NavigationExtras } from '@angular/router';
import { UserService } from '../../../core/services/user.service';
import { UserRes } from '../../../model/response/user-res.model';
import { Subscription } from 'rxjs';
import { CurrencyService } from '../../../core/services/currency.service';
import { CurrencyConvertPipe } from '../../../core/pipes/currency-convert.pipe';
import { SvgIconComponent } from "../svg-icon/svg-icon.component";
import { TenantSettingsService } from '../../../core/services/tenant-settings.service';

@Component({
  selector: 'app-avatar',
  standalone: true,
  imports: [TranslateModule, CommonModule, SvgIconComponent],
  templateUrl: './avatar.component.html',
  styleUrl: './avatar.component.css'
})
export class AvatarComponent implements OnInit, OnDestroy {
  constructor(
    private authService: AuthService,
    private router: Router,
    private userService: UserService,
    private currencyService: CurrencyService,
    private tenantSettingsService: TenantSettingsService
  ) {}

  isOpen: boolean = false;
  user: UserRes | undefined;
  formattedBalance: string = '111'; // Default value
  currentHeaderStyle: string = 'standard';
  private userSubscription: Subscription | undefined;
  isDiscountSystemEnabled = false;

  ngOnInit() {
    // Subscribe to user information
    this.userSubscription = this.userService.user$.subscribe(user => {
      this.user = user;
      if (user) {
        // Format the balance as '6.800.000đ'
        this.formattedBalance = this.formatBalance(user.balance);
      }
    });

    // Use default header style since ThemeService is removed
    this.currentHeaderStyle = 'standard';

    // Trigger getting user information if needed
    this.userService.get$.next();

    // Load tenant settings
    this.loadTenantSettings();
  }

  ngOnDestroy() {
    // Clean up subscriptions
    if (this.userSubscription) {
      this.userSubscription.unsubscribe();
    }
  }

  formatBalance(balance: number): string {
    // Use the currency service to format the balance with the user's preferred currency
    return this.currencyService.formatPrice(balance);
  }

  toggleMenu() {
    this.isOpen = !this.isOpen;
  }

  /**
   * Navigate to the settings page
   */
  goToSettings() {
    this.router.navigate(['dashboard/settings']);
    this.isOpen = false; // Close the menu after navigation
  }

  /**
   * Navigate to the settings page with security tab active
   */
  goToSecuritySettings() {
    // Navigate to settings page with query params to activate security tab
    const navigationExtras: NavigationExtras = {
      queryParams: { tab: 'security' }
    };
    this.router.navigate(['panel/settings'], navigationExtras);
    this.isOpen = false; // Close the menu after navigation
  }

  logout() {
    this.authService.logout();
   // this.router.navigate(['/auth/login']);
  }

  /**
   * Get the avatar image path based on user ID or a default one
   * @returns Path to the avatar image
   */
  getAvatarPath(): string {
    if (!this.user || !this.user.avatar) {
      // Default avatar if no user or no avatar
      return 'assets/images/profile-1.png';
    }

    // Return the avatar path based on user.avatar
    return `assets/images/${this.user.avatar}.png`;
  }

  // Load tenant settings to check if discount system is enabled
  private loadTenantSettings(): void {
    this.tenantSettingsService.getTenantInfo().subscribe({
      next: (tenantInfo) => {
        this.isDiscountSystemEnabled = tenantInfo.enable_discount_system || false;
      },
      error: (error) => {
        console.error('Error loading tenant settings:', error);
        this.isDiscountSystemEnabled = false;
      }
    });
  }

  // Get title rank display text
  getTitleRankDisplay(): string {
    if (!this.isDiscountSystemEnabled || !this.user?.title_rank) {
      return 'agent'; // Default fallback
    }
    return this.user.title_rank.name || 'agent';
  }

  // Get title rank icon
  getTitleRankIcon(): string | null {
    if (!this.isDiscountSystemEnabled || !this.user?.title_rank) {
      return null;
    }
    return this.user.title_rank.icon_name || null;
  }
}
