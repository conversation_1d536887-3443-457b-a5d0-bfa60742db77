import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Inject, PLATFORM_ID } from '@angular/core';
import { CommonModule, isPlatformBrowser } from '@angular/common';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { TranslateModule, TranslateService } from '@ngx-translate/core';

import { UserService } from '../../core/services/user.service';
import { UserRes } from '../../model/response/user-res.model';
import { ToastService } from '../../core/services/toast.service';
import { LiteDropdownComponent } from '../common/lite-dropdown/lite-dropdown.component';
import { LanguageService } from '../../core/services/language.service';
import { LoginHistoryItem } from '../../model/response/login-history.model';
import { Subscription } from 'rxjs';
import { take } from 'rxjs/operators';
// ToggleSwitchComponent removed as we're using buttons instead
import { ActivatedRoute } from '@angular/router';
import { EditUserReq } from '../../model/request/edit-user-req.model';
import { TimezoneReq } from '../../model/request/timezone-req.model';
import { MfaSettingComponent } from '../popup/mfa-setting/mfa-setting.component';
import { MfaDisabledComponent } from '../popup/mfa-disabled/mfa-disabled.component';
import { IconsModule } from '../../icons/icons.module';
import { passwordValidator } from '../../core/common/password-validator';

@Component({
  selector: 'app-profile',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    TranslateModule,
    IconsModule,
    LiteDropdownComponent,
    MfaSettingComponent,
    MfaDisabledComponent
],
  templateUrl: './profile.component.html',
  styleUrls: ['./profile.component.css', './online-dot.css']
})
export class ProfileComponent implements OnInit, OnDestroy {
  profileForm: FormGroup;
  passwordForm: FormGroup;
  user: UserRes | undefined;
  isLoading = false;
  isPasswordLoading = false;
  isVerifying = false;
  loginHistory: LoginHistoryItem[] = [];
  isLoadingHistory = false;
  private subscriptions: Subscription[] = [];
  passwordError: string | null = null;
  profileError: string | null = null;

  // Tab navigation
  activeTab: 'account' | 'security' | 'settings' | 'history' = 'account';

  // Password visibility toggles
  showCurrentPassword = false;
  showNewPassword = false;
  showConfirmPassword = false;

  // Pagination for login history
  currentPage = 1;
  totalPages = 1;
  itemsPerPage = 10;

  // Security options
  is2FAEnabled = false;

  // Settings options
  languageOptions = ['English', 'Tiếng Việt'];
  selectedLanguage = 'English';

  

timezoneOptions = [
  '(UTC-12:00) Baker/Howland Island',
  '(UTC-11:00) Niue',
  '(UTC-10:00) Hawaii-Aleutian Standard Time, Cook Islands, Tahiti',
  '(UTC-9:30) Marquesas Islands',
  '(UTC-9:00) Alaska Standard Time, Gambier Islands',
  '(UTC-8:00) Pacific Standard Time, Clipperton Island',
  '(UTC-7:00) Mountain Standard Time',
  '(UTC-6:00) Central Standard Time',
  '(UTC-5:00) Eastern Standard Time, Western Caribbean Standard Time',
  '(UTC-4:30) Venezuelan Standard Time',
  '(UTC-4:00) Atlantic Standard Time, Eastern Caribbean Standard Time',
  '(UTC-3:30) Newfoundland Standard Time',
  '(UTC-3:00) Argentina, Brazil, French Guiana, Uruguay',
  '(UTC-2:00) South Georgia/South Sandwich Islands',
  '(UTC-1:00) Azores, Cape Verde Islands',
  '(UTC+0:00) Greenwich Mean Time, Western European Time',
  '(UTC+1:00) Central European Time, West Africa Time',
  '(UTC+2:00) Central Africa Time, Eastern European Time, Kaliningrad Time',
  '(UTC+3:00) Moscow Time, East Africa Time, Arabia Standard Time',
  '(UTC+3:30) Iran Standard Time',
  '(UTC+4:00) Azerbaijan Standard Time, Samara Time',
  '(UTC+4:30) Afghanistan',
  '(UTC+5:00) Pakistan Standard Time, Yekaterinburg Time',
  '(UTC+5:30) Indian Standard Time, Sri Lanka Time',
  '(UTC+5:45) Nepal Time',
  '(UTC+6:00) Bangladesh Standard Time, Bhutan Time, Omsk Time',
  '(UTC+6:30) Cocos Islands, Myanmar',
  '(UTC+7:00) Krasnoyarsk Time, Cambodia, Laos, Thailand, Vietnam',
  '(UTC+8:00) Australian Western Standard Time, Beijing Time, Irkutsk Time',
  '(UTC+8:45) Australian Central Western Standard Time',
  '(UTC+9:00) Japan Standard Time, Korea Standard Time, Yakutsk Time',
  '(UTC+9:30) Australian Central Standard Time',
  '(UTC+10:00) Australian Eastern Standard Time, Vladivostok Time',
  '(UTC+10:30) Lord Howe Island',
  '(UTC+11:00) Srednekolymsk Time, Solomon Islands, Vanuatu',
  '(UTC+11:30) Norfolk Island',
  '(UTC+12:00) Fiji, Gilbert Islands, Kamchatka Time, New Zealand Standard Time',
  '(UTC+12:45) Chatham Islands Standard Time',
  '(UTC+13:00) Samoa Time Zone, Phoenix Islands Time, Tonga',
  '(UTC+14:00) Line Islands'
];
  selectedTimezone = '(UTC+7:00) Krasnoyarsk Time, Cambodia, Laos, Thailand, Vietnam';
  formattedBalance: string = '';
  constructor(
    private fb: FormBuilder,
    private userService: UserService,
    private toast: ToastService,
    private translate: TranslateService,
    private languageService: LanguageService,
    private route: ActivatedRoute,
    @Inject(PLATFORM_ID) private platformId: Object
  ) {
    // Initialize profile form
    this.profileForm = this.fb.group({
      email: ['', [Validators.required, Validators.email]],
      phone: ['', [Validators.required]]
    });

    // Initialize password form
    this.passwordForm = this.fb.group({
      oldPassword: ['', [Validators.required]],
      newPassword: ['', [Validators.minLength(8), Validators.required, passwordValidator()]],
      confirmPassword: ['', [Validators.required]]
    }, {
      validators: this.passwordMatchValidator
    });
  }

  ngOnInit(): void {
    // Check for tab parameter in URL query params
    this.route.queryParams.subscribe(params => {
      if (params['tab']) {
        // Check if the tab parameter is valid
        const tabParam = params['tab'];
        if (tabParam === 'account' || tabParam === 'security' ||
            tabParam === 'settings' || tabParam === 'history') {
          this.activeTab = tabParam as 'account' | 'security' | 'settings' | 'history';
        }
      }
    });

    // Subscribe to user data from the UserService - use take(1) to only get the current value
    const userSub = this.userService.user$.subscribe(user => {
      this.user = user;

      if (user) {
        // Populate form with user data
        this.profileForm.patchValue({
          email: user.email || '',
          phone: user.phone || ''
        });
        this.formattedBalance = user.balance?.toFixed(6).replace(/\.?0+$/, '') || '0';

        // Load login history only once
        this.loadLoginHistory();

        // Load security settings
        this.loadSecuritySettings();

        // Initialize timezone from user data
        this.initializeCurrentTimezone();
      } else {
        // If user is not loaded yet, trigger a fetch
        this.userService.get$.next();
      }
    });
    this.subscriptions.push(userSub);

    // Initialize language based on current setting from localStorage and TranslateService
    this.initializeCurrentLanguage();

    // Subscribe to language changes
    const languageChangeSub = this.translate.onLangChange.subscribe((event: any) => {
      this.updateSelectedLanguageFromCode(event.lang);
    });
    this.subscriptions.push(languageChangeSub);
  }

  // Load security settings from user data
  loadSecuritySettings(): void {
    if (this.user) {
      // Check if 2FA is enabled (assuming the user object has this information)
      this.is2FAEnabled = this.user.mfa_enabled || false;
    }
  }

  ngOnDestroy(): void {
    // Clean up subscriptions to prevent memory leaks
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }

  // Load login history
  loadLoginHistory(): void {
    console.log('Loading login history...');
    this.isLoadingHistory = true;

    // Set a default empty array to prevent undefined errors
    this.loginHistory = [];

    const historySub = this.userService.getLoginHistory().subscribe({
      next: (response) => {
        console.log('Login history API response:', response);

        if (response && response.content) {
          this.loginHistory = [...response.content]; // Create a new array to trigger change detection
          console.log('Login history items:', this.loginHistory.length);

          // Add sample items for testing if the array is empty
          // if (this.loginHistory.length === 0 && response.content.length === 0) {
          //   console.log('Adding sample login history items for testing');
          //   // Sample data for testing
          //   this.loginHistory = [
          //     {
          //       id: 1,
          //       ip: '***********',
          //       user_agent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) Chrome/*********',
          //       created_at: new Date().toISOString()
          //     },
          //     {
          //       id: 2,
          //       ip: '***********',
          //       user_agent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) Safari/605.1.15',
          //       created_at: new Date(Date.now() - 86400000).toISOString() // 1 day ago
          //     },
          //     {
          //       id: 3,
          //       ip: '***********',
          //       user_agent: 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_0 like Mac OS X) Mobile/15E148',
          //       created_at: new Date(Date.now() - 172800000).toISOString() // 2 days ago
          //     }
          //   ];
          // }

          // Calculate pagination
          this.totalPages = Math.ceil(this.loginHistory.length / this.itemsPerPage);
          if (this.totalPages === 0) this.totalPages = 1;
          this.currentPage = 1;
        } else {
          // If response doesn't have the expected structure, keep the empty array
          console.warn('Login history response did not have expected structure:', response);
        }

        // Log the final state for debugging
        console.log('Final login history state:', this.loginHistory);
        this.isLoadingHistory = false;
      },
      error: (error) => {
        console.error('Error loading login history:', error);
        this.loginHistory = []; // Ensure loginHistory is initialized even on error
        this.isLoadingHistory = false;
        this.toast.showError('Failed to load login history');
      }
    });

    this.subscriptions.push(historySub);
  }

  // Pagination methods
  nextPage(): void {
    if (this.currentPage < this.totalPages) {
      this.currentPage++;
    }
  }

  prevPage(): void {
    if (this.currentPage > 1) {
      this.currentPage--;
    }
  }

  get paginatedLoginHistory(): LoginHistoryItem[] {
    const startIndex = (this.currentPage - 1) * this.itemsPerPage;
    const endIndex = startIndex + this.itemsPerPage;
    return this.loginHistory.slice(startIndex, endIndex);
  }

  // Format date for display with user's timezone
  formatDate(dateString: string | null | undefined): string {
    if (!dateString) {
      return 'N/A';
    }
    try {
      const date = new Date(dateString);

      // If the date string already contains timezone info (like 2025-06-10T10:55:53.632484+08:00)
      // we want to display it in the user's preferred timezone
      if (this.user && this.user.time_zone) {
        // Create timezone-aware formatting options
        const options: Intl.DateTimeFormatOptions = {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit',
          timeZone: this.getTimezoneFromOffset(this.user.time_zone),
          hour12: false
        };

        return date.toLocaleString('en-GB', options);
      }

      // Fallback to default locale string
      return date.toLocaleString();
    } catch (error) {
      console.error('Error formatting date:', error);
      return dateString;
    }
  }

  // Helper method to convert timezone offset to timezone identifier
  private getTimezoneFromOffset(offset: string): string {
    const timezoneMap: { [key: string]: string } = {
      '-12:00': 'Etc/GMT+12',
      '-11:00': 'Etc/GMT+11',
      '-10:00': 'Pacific/Honolulu',
      '-09:30': 'Pacific/Marquesas',
      '-09:00': 'America/Anchorage',
      '-08:00': 'America/Los_Angeles',
      '-07:00': 'America/Denver',
      '-06:00': 'America/Chicago',
      '-05:00': 'America/New_York',
      '-04:30': 'America/Caracas',
      '-04:00': 'America/Halifax',
      '-03:30': 'America/St_Johns',
      '-03:00': 'America/Sao_Paulo',
      '-02:00': 'Etc/GMT+2',
      '-01:00': 'Atlantic/Azores',
      '+00:00': 'Europe/London',
      '+01:00': 'Europe/Berlin',
      '+02:00': 'Europe/Athens',
      '+03:00': 'Europe/Moscow',
      '+03:30': 'Asia/Tehran',
      '+04:00': 'Asia/Dubai',
      '+04:30': 'Asia/Kabul',
      '+05:00': 'Asia/Karachi',
      '+05:30': 'Asia/Kolkata',
      '+05:45': 'Asia/Kathmandu',
      '+06:00': 'Asia/Dhaka',
      '+06:30': 'Asia/Yangon',
      '+07:00': 'Asia/Bangkok',
      '+08:00': 'Asia/Singapore',
      '+08:45': 'Australia/Eucla',
      '+09:00': 'Asia/Tokyo',
      '+09:30': 'Australia/Adelaide',
      '+10:00': 'Australia/Sydney',
      '+10:30': 'Australia/Lord_Howe',
      '+11:00': 'Pacific/Guadalcanal',
      '+11:30': 'Pacific/Norfolk',
      '+12:00': 'Pacific/Auckland',
      '+12:45': 'Pacific/Chatham',
      '+13:00': 'Pacific/Tongatapu',
      '+14:00': 'Pacific/Kiritimati'
    };

    return timezoneMap[offset] || 'Asia/Bangkok'; // Default to Bangkok timezone
  }

  /**
   * Get the avatar image path based on user ID or a default one
   * @returns Path to the avatar image
   */
  getAvatarPath(): string {
    if (!this.user || !this.user.avatar) {
      // Default avatar if no user or no avatar
      return 'assets/images/profile-1.png';
    }

    // Return the avatar path based on user.avatar
    return `assets/images/${this.user.avatar}.png`;
  }

  // Properties for MFA popups
  showMfaSettings = false;
  showMfaDisabled = false;

  // Toggle 2FA status
  toggle2FA(): void {
    console.log('toggle2FA called, is2FAEnabled:', this.is2FAEnabled);

    if (this.is2FAEnabled) {
      // If 2FA is enabled, show the disable popup
      console.log('Showing MFA disable popup');
      this.showMfaDisabled = true;
    } else {
      // If 2FA is disabled, show the enable popup
      this.showEnableMfa();
    }
  }

  // Show enable MFA popup
  showEnableMfa(): void {
    console.log('showEnableMfa called');
    this.showMfaSettings = true;
  }

  // Show disable MFA popup
  showDisableMfa(): void {
    console.log('showDisableMfa called');
    this.showMfaDisabled = true;
  }

  // Close MFA settings popup
  closeMfaSettings(): void {
    console.log('Closing MFA settings popup');
    this.showMfaSettings = false;
  }

  // Close MFA disabled popup
  closeMfaDisabled(): void {
    console.log('Closing MFA disabled popup');
    this.showMfaDisabled = false;
  }

  // Handle MFA enabled event
  onMfaEnabled(): void {
    this.is2FAEnabled = true;
    // Update user data
    this.userService.get$.next();
  }

  // Handle MFA disabled event
  onMfaDisabled(): void {
    this.is2FAEnabled = false;
    // Update user data
    this.userService.get$.next();
  }

  // API Key related methods removed as requested

  // Initialize current language from localStorage and TranslateService
  private initializeCurrentLanguage(): void {
    if (!isPlatformBrowser(this.platformId)) {
      // For server-side rendering, use default language
      this.updateSelectedLanguageFromCode('vi');
      return;
    }

    // Get language from localStorage or TranslateService
    const currentLang = localStorage.getItem('language') || this.translate.currentLang || 'vi';
    this.updateSelectedLanguageFromCode(currentLang);
  }

  // Update selected language display based on language code
  private updateSelectedLanguageFromCode(langCode: string): void {
    this.selectedLanguage = langCode === 'en' ? 'English' : 'Tiếng Việt';
  }

  // Initialize current timezone from user data
  private initializeCurrentTimezone(): void {
    if (this.user && this.user.time_zone) {
      // Find the matching timezone option based on the user's timezone offset
      const userTimezone = this.user.time_zone;

      // First try to find exact match by offset pattern
      const offsetMatch = this.timezoneOptions.find(option => {
        const optionOffset = option.match(/UTC([+-]\d{1,2}:\d{2})/);
        if (optionOffset) {
          // Normalize both offsets for comparison
          const normalizedOptionOffset = this.normalizeTimezoneOffsetForComparison(optionOffset[1]);
          const normalizedUserTimezone = this.normalizeTimezoneOffsetForComparison(userTimezone);
          return normalizedOptionOffset === normalizedUserTimezone;
        }
        return false;
      });

      if (offsetMatch) {
        this.selectedTimezone = offsetMatch;
      } else {
        // If no exact match found, try to find by string inclusion
        const matchingOption = this.timezoneOptions.find(option =>
          option.includes(userTimezone)
        );

        if (matchingOption) {
          this.selectedTimezone = matchingOption;
        }
      }
    }
  }

  /**
   * Normalize timezone offset for comparison purposes
   * @param offset Timezone offset (e.g., "+7:00", "+07:00")
   * @returns Normalized offset with leading zeros (e.g., "+07:00")
   */
  private normalizeTimezoneOffsetForComparison(offset: string): string {
    const parts = offset.split(':');
    if (parts.length !== 2) return offset;

    const sign = parts[0].charAt(0);
    const hours = parts[0].substring(1).padStart(2, '0');
    const minutes = parts[1];

    return `${sign}${hours}:${minutes}`;
  }

  // Helper method to extract timezone offset from display string
  private extractTimezoneOffset(timezoneDisplay: string): string {
    // Match patterns like (UTC+7:00), (UTC-12:00), (UTC+5:30), etc.
    const match = timezoneDisplay.match(/UTC([+-]\d{1,2}:\d{2})/);
    if (match) {
      const offset = match[1];
      // Ensure leading zero for single-digit hours
      const parts = offset.split(':');
      const sign = parts[0].charAt(0);
      const hours = parts[0].substring(1).padStart(2, '0');
      const minutes = parts[1];
      return `${sign}${hours}:${minutes}`;
    }
    return '+07:00'; // Default to +07:00 if no match
  }

  // Settings methods
  async onLanguageChange(language: string): Promise<void> {
    this.selectedLanguage = language;
    const langCode = language === 'English' ? 'en' : 'vi';

    try {
      // Use the language service to properly change language (handles localStorage and API)
      await this.languageService.changeLanguage(langCode);
      this.toast.showSuccess(`Language changed to ${language}`);
    } catch (error) {
      console.error('Error changing language:', error);
      this.toast.showError('Failed to change language');
      // Revert the selection on error
      this.initializeCurrentLanguage();
    }
  }

  async onTimezoneChange(timezone: string): Promise<void> {
    const previousTimezone = this.selectedTimezone;
    this.selectedTimezone = timezone;

    // Extract the timezone offset from the selected option
    const timezoneOffset = this.extractTimezoneOffset(timezone);

    try {
      // Call the API to update timezone
      const timezoneReq: TimezoneReq = {
        timezone: timezoneOffset
      };

      await this.userService.setTimezone(timezoneReq).toPromise();
      this.toast.showSuccess('Timezone updated successfully');

      // Update user data to reflect the change
      if (this.user) {
        this.user = {
          ...this.user,
          time_zone: timezoneOffset
        };
      }
    } catch (error) {
      console.error('Error updating timezone:', error);
      this.toast.showError('Failed to update timezone');
      // Revert the selection on error
      this.selectedTimezone = previousTimezone;
    }
  }

  // Custom validator to check if passwords match
  passwordMatchValidator(group: FormGroup) {
    const newPassword = group.get('newPassword')?.value;
    const confirmPassword = group.get('confirmPassword')?.value;

    if (newPassword && confirmPassword && newPassword !== confirmPassword) {
      group.get('confirmPassword')?.setErrors({ passwordMismatch: true });
      return { passwordMismatch: true };
    } else if (group.get('confirmPassword')?.hasError('passwordMismatch')) {
      // Clear the error if passwords now match
      group.get('confirmPassword')?.setErrors(null);
    }

    return null;
  }

  // Verify email
  verifyEmail(): void {
    this.isVerifying = true;
    // Implement email verification logic here
    setTimeout(() => {
      this.isVerifying = false;
      this.toast.showSuccess('Verification email sent');
    }, 1000);
  }

  // Update profile
  onSubmit(): void {
    // Reset error message
    this.profileError = null;

    if (this.profileForm.invalid) {
      // Mark all fields as touched to trigger validation messages
      Object.keys(this.profileForm.controls).forEach(key => {
        const control = this.profileForm.get(key);
        control?.markAsTouched();
      });
      return;
    }

    this.isLoading = true;

    // Update user info (email and phone)
    const userInfo: EditUserReq = {
      email: this.profileForm.value.email || '',
      phone: this.profileForm.value.phone || ''
    };

    this.userService.updateInfo(userInfo).subscribe({
      next: (updatedUser) => {
        this.isLoading = false;
        // Update only specific fields in the user object
        if (updatedUser && this.user) {
          // Only update the email and phone fields
          this.user = {
            ...this.user,
            email: updatedUser.email,
            phone: updatedUser.phone
          };
        }
        this.toast.showSuccess(this.translate.instant('profile.profile_updated'));
      },
      error: (err) => {
        this.isLoading = false;
        console.error('Profile update error:', err);

        // Set error message based on the response
        if (err && err.message) {
          this.profileError = err.message;
        } else {
          this.profileError = 'Failed to update profile. Please try again.';
        }

        this.toast.showError(this.profileError || 'Failed to update profile');
      }
    });
  }

  // Change password
  onChangePassword(): void {
    // Reset error message
    this.passwordError = null;

    if (this.passwordForm.invalid) {
      // Mark all fields as touched to trigger validation messages
      Object.keys(this.passwordForm.controls).forEach(key => {
        const control = this.passwordForm.get(key);
        control?.markAsTouched();
      });
      return;
    }

    this.isPasswordLoading = true;

    const passwordData = {
      oldPassword: this.passwordForm.value.oldPassword,
      newPassword: this.passwordForm.value.newPassword
    };

    this.userService.changePass({
      old_password: passwordData.oldPassword,
      new_password: passwordData.newPassword
    }).subscribe({
      next: () => {
        this.isPasswordLoading = false;
        this.toast.showSuccess(this.translate.instant('profile.password_updated'));
        // Reset form
        this.passwordForm.reset();
      },
      error: (err) => {
        this.isPasswordLoading = false;
        console.error('Password update error:', err);

        // Set error message based on the response
        if (err && err.message) {
          this.passwordError = err.message;
        } else {
          this.passwordError = 'Failed to update password. Please try again.';
        }

        this.toast.showError(this.passwordError || 'Failed to update password');
      }
    });
  }
}
