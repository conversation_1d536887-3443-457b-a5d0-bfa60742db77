import { CommonModule } from '@angular/common';
import { Component, OnInit, OnDestroy } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { Observable, BehaviorSubject, Subscription, combineLatest } from 'rxjs';
import { map } from 'rxjs/operators';

// Services
import { PaymentMethodService, PaymentMethodResponse } from '../../core/services/payment-method.service';
import { TransactionService } from '../../core/services/transaction.service';
import { UserService } from '../../core/services/user.service';
import { CurrencyService } from '../../core/services/currency.service';

// Components
import { DateRangePickerComponent } from '../common/date-range-picker/date-range-picker.component';

// Models
import { MyTransactionRes, TransactionSearchReq } from '../../model/response/my-transaction.model';
import { UserRes } from '../../model/response/user-res.model';

interface TableHeader {
  key: string;
  label: string;
}

@Component({
  selector: 'app-add-fund',
  standalone: true,
  imports: [CommonModule, FormsModule, TranslateModule, DateRangePickerComponent],
  templateUrl: './add-fund.component.html',
  styleUrl: './add-fund.component.css'
})
export class AddFundComponent implements OnInit, OnDestroy {
  private subscriptions: Subscription[] = [];

  // State management
  private _state$ = new BehaviorSubject({
    // User data
    currentUser: null as UserRes | null,
    
    // Payment method selection
    paymentMethods: [] as PaymentMethodResponse[],
    filteredPaymentMethods: [] as PaymentMethodResponse[],
    selectedPaymentMethod: null as PaymentMethodResponse | null,
    searchTerm: '',
    showDropdown: false,
    
    // Loading states
    isLoadingPaymentMethods: false,
    isLoadingTransactions: false,
    
    // Amount selection
    amountOptions: [10000, 50000, 100000, 250000],
    selectedAmount: 10000,
    customAmount: '',
    
    // Amount validation
    amountValidation: {
      isValid: true,
      message: ''
    },
    
    // Transaction history
    transactions: [] as MyTransactionRes[],
    dateRange: {
      startDate: null as Date | null,
      endDate: null as Date | null
    },
    
    // Transaction totals
    totalDeposit: 0,
    totalBonus: 0
  });

  public state$ = this._state$.asObservable();

  // Transaction history table headers
  tableHeaders: TableHeader[] = [
    { key: 'id', label: 'add_fund.transaction_id' },
    { key: 'created_at', label: 'add_fund.date' },
    { key: 'type', label: 'add_fund.type' },
    { key: 'change', label: 'add_fund.amount' }
  ];

  constructor(
    private paymentMethodService: PaymentMethodService,
    private transactionService: TransactionService,
    private userService: UserService,
    private currencyService: CurrencyService
  ) {}

  ngOnInit(): void {
    this.initialize();
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }

  private initialize(): void {
    // Set default date range to current month
    const now = new Date();
    const firstDay = new Date(now.getFullYear(), now.getMonth(), 1);
    const lastDay = new Date(now.getFullYear(), now.getMonth() + 1, 0);

    this.updateState({
      dateRange: {
        startDate: firstDay,
        endDate: lastDay
      }
    });

    this.loadUserData();
    this.loadPaymentMethods();
    this.loadTransactions();
  }

  private updateState(updates: Partial<typeof this._state$.value>): void {
    const currentState = this._state$.value;
    this._state$.next({ ...currentState, ...updates });
  }

  private loadUserData(): void {
    this.subscriptions.push(
      this.userService.user$.subscribe(currentUser => {
        this.updateState({ currentUser });
      })
    );
  }

  private loadPaymentMethods(): void {
    this.updateState({ isLoadingPaymentMethods: true });

    const subscription = this.paymentMethodService.getActivePaymentMethodsFromMainTenant().subscribe({
      next: (methods) => {
        // Methods are already filtered to main tenant only
        const activeMethods = methods.filter(method => method.is_active);

        this.updateState({
          paymentMethods: activeMethods,
          filteredPaymentMethods: activeMethods,
          isLoadingPaymentMethods: false
        });

        // Auto-select first method if available
        if (activeMethods.length > 0 && !this._state$.value.selectedPaymentMethod) {
          this.selectPaymentMethod(activeMethods[0]);
        }
      },
      error: (error) => {
        console.error('Error loading payment methods from main tenant:', error);
        this.updateState({ isLoadingPaymentMethods: false });
      }
    });

    this.subscriptions.push(subscription);
  }

  private loadTransactions(): void {
    this.updateState({ isLoadingTransactions: true });

    const currentState = this._state$.value;
    const searchReq: TransactionSearchReq = {
      types: ['Deposit', 'Bonus']
    };

    if (currentState.dateRange.startDate) {
      searchReq.from = this.formatDateForAPI(currentState.dateRange.startDate);
    }

    if (currentState.dateRange.endDate) {
      searchReq.to = this.formatDateForAPI(currentState.dateRange.endDate);
    }

    const subscription = this.transactionService.searchTransactions(searchReq, 0, 50).subscribe({
      next: (response) => {
        const transactions = response.content as MyTransactionRes[];
        this.updateState({
          transactions,
          isLoadingTransactions: false
        });
        this.calculateTotals();
      },
      error: (error) => {
        console.error('Error loading transactions:', error);
        this.updateState({ isLoadingTransactions: false });
      }
    });

    this.subscriptions.push(subscription);
  }

  private calculateTotals(): void {
    const transactions = this._state$.value.transactions;
    const totalDeposit = transactions
      .filter(t => t.type === 'Deposit')
      .reduce((sum, t) => sum + t.change, 0);

    const totalBonus = transactions
      .filter(t => t.type === 'Bonus')
      .reduce((sum, t) => sum + t.change, 0);

    this.updateState({
      totalDeposit,
      totalBonus
    });
  }

  // Payment method dropdown functions
  selectPaymentMethod(method: PaymentMethodResponse): void {
    // Generate amount options based on payment method limits
    const amountOptions = this.generateAmountOptions(method);

    // Set selected amount to minimum if current selection is below minimum
    let selectedAmount = this._state$.value.selectedAmount;
    if (selectedAmount < method.min_amount) {
      selectedAmount = method.min_amount;
    } else if (selectedAmount > method.max_amount) {
      selectedAmount = method.min_amount;
    }

    // Clear custom amount if it's invalid
    let customAmount = this._state$.value.customAmount;
    const customAmountNum = parseFloat(customAmount);
    if (customAmount && !isNaN(customAmountNum) &&
        (customAmountNum < method.min_amount || customAmountNum > method.max_amount)) {
      customAmount = '';
    }

    this.updateState({
      selectedPaymentMethod: method,
      showDropdown: false,
      searchTerm: '',
      filteredPaymentMethods: this._state$.value.paymentMethods,
      amountOptions,
      selectedAmount,
      customAmount
    });

    this.validateCurrentAmount();
  }

  toggleDropdown(): void {
    const showDropdown = !this._state$.value.showDropdown;
    this.updateState({
      showDropdown,
      searchTerm: showDropdown ? this._state$.value.searchTerm : '',
      filteredPaymentMethods: showDropdown ? this._state$.value.filteredPaymentMethods : this._state$.value.paymentMethods
    });
  }

  onSearchChange(searchTerm: string): void {
    const filteredPaymentMethods = this.filterPaymentMethods(
      this._state$.value.paymentMethods,
      searchTerm
    );
    this.updateState({
      searchTerm,
      filteredPaymentMethods
    });
  }

  private filterPaymentMethods(methods: PaymentMethodResponse[], searchTerm: string): PaymentMethodResponse[] {
    if (!searchTerm.trim()) {
      return methods;
    }

    const term = searchTerm.toLowerCase();
    return methods.filter(method =>
      method.name.toLowerCase().includes(term) ||
      method.bank_name.toLowerCase().includes(term) ||
      method.id.toString().includes(term)
    );
  }

  // Check if payment method has bonus conditions
  hasBonus(method: PaymentMethodResponse): boolean | undefined {
    return method.conditions && method.conditions.length > 0;
  }

  // Generate QR code URL using VietQR format
  getQRCodeUrl(): string {
    const currentState = this._state$.value;
    const amount = this.getEffectiveAmount();
    const method = currentState.selectedPaymentMethod;
    
    if (!method) return '';

    const bankCode = method.bank_code;
    const accountNumber = method.account_number;
    const accountName = encodeURIComponent(method.account_name);
    const addInfo = encodeURIComponent(`NAP ${currentState.currentUser?.user_name || 'user'}`);

    return `https://img.vietqr.io/image/${bankCode}-${accountNumber}-qr_only.jpg?amount=${amount}&accountName=${accountName}&addInfo=${addInfo}`;
  }

  getBankIconUrl(icon: string): string {
    if (!icon) return '';
    return `${icon}`;
  }

  // Check if payment method is manual
  isManualPaymentMethod(method: PaymentMethodResponse): boolean {
    return method.provider_id === 'manual';
  }

  // Get manual payment method info
  getManualPaymentInfo(method: PaymentMethodResponse): any {
    if (!this.isManualPaymentMethod(method) || !method.addition_info) {
      return null;
    }

    try {
      return JSON.parse(method.addition_info);
    } catch (error) {
      console.error('Error parsing manual payment info:', error);
      return null;
    }
  }

  // Date range change handler
  onDateRangeChange(dateRange: { startDate: Date | null, endDate: Date | null }): void {
    this.updateState({ dateRange });
    this.loadTransactions();
  }

  // Format currency
  formatCurrency(amount: number): string {
    const method = this._state$.value.selectedPaymentMethod;
    return amount + (method?.bank_currency_code || '');
  }

  // Format date
  formatDate(dateString: string): string {
    const date = new Date(dateString);
    return date.toLocaleDateString();
  }

  private formatDateForAPI(date: Date): string {
    return date.toISOString().split('T')[0];
  }

  // Copy to clipboard functionality
  async copyToClipboard(text: string): Promise<void> {
    try {
      await navigator.clipboard.writeText(text);
      console.log('Text copied to clipboard:', text);
    } catch (error) {
      console.error('Failed to copy text to clipboard:', error);
    }
  }

  async copyAccountNumber(): Promise<void> {
    const method = this._state$.value.selectedPaymentMethod;
    if (method?.account_number) {
      await this.copyToClipboard(method.account_number);
    }
  }

  async copyAccountName(): Promise<void> {
    const method = this._state$.value.selectedPaymentMethod;
    if (method?.account_name) {
      await this.copyToClipboard(method.account_name);
    }
  }

  async copyBankName(): Promise<void> {
    const method = this._state$.value.selectedPaymentMethod;
    if (method?.bank_name) {
      await this.copyToClipboard(method.bank_name);
    }
  }

  getTransferContent(): string {
    const currentState = this._state$.value;
    const username = currentState.currentUser?.user_name || 'user';
    return `NAP ${username}`;
  }

  async copyTransferContent(): Promise<void> {
    await this.copyToClipboard(this.getTransferContent());
  }

  // Generate amount options based on payment method limits
  private generateAmountOptions(method: PaymentMethodResponse): number[] {
    const minAmount = method.min_amount;
    const maxAmount = method.max_amount;

    // Base options starting from minimum amount
    const baseOptions = [
      minAmount,
      minAmount * 2,
      minAmount * 5,
      minAmount * 10
    ];

    // Filter options that are within the max limit and remove duplicates
    const validOptions = baseOptions
      .filter(option => option <= maxAmount)
      .filter((value, index, array) => array.indexOf(value) === index) // Remove duplicates
      .sort((a, b) => a - b); // Sort ascending

    // If we have less than 4 options, add some more reasonable amounts
    if (validOptions.length < 4) {
      const additionalOptions = [50000, 100000, 250000, 500000, 1000000];
      for (const option of additionalOptions) {
        if (option >= minAmount && option <= maxAmount && !validOptions.includes(option)) {
          validOptions.push(option);
          if (validOptions.length >= 4) break;
        }
      }
      validOptions.sort((a, b) => a - b);
    }

    return validOptions.slice(0, 4); // Return maximum 4 options
  }

  // Amount selection methods
  updateSelectedAmount(amount: number): void {
    this.updateState({ selectedAmount: amount, customAmount: '' });
    this.validateCurrentAmount();
  }

  updateCustomAmount(amount: string): void {
    this.updateState({ customAmount: amount });
    this.validateCurrentAmount();
  }

  // Get current effective amount
  private getEffectiveAmount(): number {
    const currentState = this._state$.value;
    const customAmount = parseFloat(currentState.customAmount);
    return !isNaN(customAmount) && customAmount > 0 ? customAmount : currentState.selectedAmount;
  }

  // Validate current amount against payment method limits
  private validateCurrentAmount(): void {
    const method = this._state$.value.selectedPaymentMethod;
    if (!method) {
      this.updateState({
        amountValidation: {
          isValid: false,
          message: 'Please select a payment method'
        }
      });
      return;
    }

    const amount = this.getEffectiveAmount();

    if (amount <= 0) {
      this.updateState({
        amountValidation: {
          isValid: false,
          message: 'Amount must be greater than 0'
        }
      });
      return;
    }

    if (amount < method.min_amount) {
      this.updateState({
        amountValidation: {
          isValid: false,
          message: `Minimum amount is ${method.min_amount}`
        }
      });
      return;
    }

    if (amount > method.max_amount) {
      this.updateState({
        amountValidation: {
          isValid: false,
          message: `Maximum amount is ${method.max_amount}`
        }
      });
      return;
    }

    this.updateState({
      amountValidation: {
        isValid: true,
        message: 'Amount is valid'
      }
    });
  }

  // Get current amount
  getCurrentAmount(): number {
    return this.getEffectiveAmount();
  }

  // Check if current amount is valid
  isCurrentAmountValid(): boolean {
    return this._state$.value.amountValidation.isValid;
  }

  // Get current amount validation message
  getAmountValidationMessage(): string {
    return this._state$.value.amountValidation.message;
  }
}
