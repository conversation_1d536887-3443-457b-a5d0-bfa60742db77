import {
  HttpErrorResponse,
  HttpHandlerFn,
  HttpRequest,
  HttpClient,
  HttpHeaders
} from '@angular/common/http';
import { PLATFORM_ID, inject } from '@angular/core';
import { isPlatformBrowser } from '@angular/common';

import { BehaviorSubject, Observable, of, switchMap, throwError } from 'rxjs';
import { catchError, filter, take, finalize } from 'rxjs/operators';
import { AuthUtilsService } from '../services/auth-utils.service';
import { UserRes } from '../../model/response/user-res.model';
import { Router } from '@angular/router';
import { ConfigService } from '../services/config.service';

// Shared state for the interceptor function
let isRefreshing = false;
const refreshTokenSubject = new BehaviorSubject<any>(null);

export function jwtInterceptorFn(request: HttpRequest<unknown>, next: HttpHandlerFn): Observable<any> {
  const platformId = inject(PLATFORM_ID);
  const authUtils = inject(AuthUtilsService);
  const router = inject(Router);
  const http = inject(HttpClient);
  const configService = inject(ConfigService);

  // Skip token handling on server-side
  if (!isPlatformBrowser(platformId)) {
    return next(request);
  }

  const user = authUtils.getUserFromStorage();
  const isApiUrl = request.url.startsWith(configService.apiUrl);

  // Check if the request is for login, registration, or refresh token
  const isLoginRequest = request.url.includes('/access/login');
  //const isMafaRequest = request.url.includes('/access/mfa');
  const isRegisterRequest = request.url.includes('/users/register');
  const isRefreshRequest = request.url.includes('/access/refresh');

  // For debugging
  // console.log('JWT Interceptor - Request URL:', request.url);
  // console.log('JWT Interceptor - Is API URL:', isApiUrl);
  // console.log('JWT Interceptor - User exists:', !!user);
  // console.log('JWT Interceptor - Is Authenticated:', user && authUtils.isAuthenticated());
  // console.log('JWT Interceptor - Is Login Request:', isLoginRequest);
  // console.log('JWT Interceptor - Is Register Request:', isRegisterRequest);
  // console.log('JWT Interceptor - Is Refresh Request:', isRefreshRequest);

  let modifiedRequest = request;

  // Add token to API requests if user is authenticated, except for login/register
  if (user && user.tokens && user.tokens.access_token && isApiUrl && !isLoginRequest &&  !isRegisterRequest) {
    console.log('JWT Interceptor - Adding token header');
    modifiedRequest = authUtils.addTokenHeader(request, user);
  }

  // Handle 401 errors for token expiration
  return next(modifiedRequest).pipe(
    catchError(error => {
      if (error instanceof HttpErrorResponse && error.status === 401 &&
          !isLoginRequest && !isRegisterRequest && !isRefreshRequest) {
        console.log('JWT Interceptor - 401 error detected, attempting to refresh token');
        return handle401Error(request, next, http, authUtils, router, configService);
      }
      return throwError(() => error);
    })
  );
}

function handle401Error(
  request: HttpRequest<any>,
  next: HttpHandlerFn,
  http: HttpClient,
  authUtils: AuthUtilsService,
  router: Router,
  configService: ConfigService
) {
  const platformId = inject(PLATFORM_ID);

  // Skip refresh token handling on server-side
  if (!isPlatformBrowser(platformId)) {
    return throwError(() => new Error('Unauthorized'));
  }

  // Check if we're already refreshing to avoid multiple refresh requests
  if (!isRefreshing) {
    isRefreshing = true;
    refreshTokenSubject.next(null);
    console.log('JWT Interceptor - Starting token refresh process');

    const user = authUtils.getUserFromStorage();
    const refreshToken = user?.tokens?.refresh_token;

    if (refreshToken) {
      console.log('JWT Interceptor - Refresh token found, attempting to refresh');

      // Create refresh request with proper headers
      const refreshHeaders = new HttpHeaders({
        'x-refresh-token': refreshToken,
        'Content-Type': 'application/json'
      });

      return http.post<UserRes>(
        `${configService.apiUrl}/access/refresh`,
        {},
        { headers: refreshHeaders }
      ).pipe(
        switchMap((newUser: UserRes) => {
          console.log('JWT Interceptor - Token refresh successful');
          isRefreshing = false;

          // Update user in localStorage
          if (isPlatformBrowser(platformId)) {
            localStorage.setItem('user', JSON.stringify(newUser));
          }

          // Notify other subscribers that the token has been refreshed
          refreshTokenSubject.next(newUser);

          // Retry the original request with the new token
          return next(authUtils.addTokenHeader(request, newUser));
        }),
        catchError((refreshError) => {
          console.error('JWT Interceptor - Token refresh failed:', refreshError);
          return handleLogout(platformId, router, refreshError);
        }),
        finalize(() => {
          isRefreshing = false;
        })
      );
    } else {
      console.log('JWT Interceptor - No refresh token available');
      return handleLogout(platformId, router, new Error('No refresh token available'));
    }
  }

  // If we're already refreshing, wait for the new token
  console.log('JWT Interceptor - Already refreshing, waiting for new token');
  return refreshTokenSubject.pipe(
    filter(token => token !== null),
    take(1),
    switchMap((newUser) => {
      console.log('JWT Interceptor - New token received, retrying request');
      return next(authUtils.addTokenHeader(request, newUser));
    }),
    catchError((error) => {
      console.error('JWT Interceptor - Error while waiting for refresh:', error);
      return throwError(() => error);
    })
  );
}

function handleLogout(platformId: Object, router: Router, error: any): Observable<never> {
  isRefreshing = false;
  refreshTokenSubject.next(null);

  // Clear all authentication data
  if (isPlatformBrowser(platformId)) {
    localStorage.removeItem('user');
    localStorage.removeItem('userEmail'); // Clear saved email too

    // Clear any other auth-related data
    const keysToRemove = ['redirectAfterLogin'];
    keysToRemove.forEach(key => {
      if (localStorage.getItem(key)) {
        localStorage.removeItem(key);
      }
    });

    // Only redirect if not already on auth page
    if (!router.url.includes('/auth/')) {
      console.log('JWT Interceptor - Redirecting to login after authentication failure');
      // Don't store redirect URL for failed auth
      router.navigate(['/auth/login']);
    }
  }

  return throwError(() => error);
}


