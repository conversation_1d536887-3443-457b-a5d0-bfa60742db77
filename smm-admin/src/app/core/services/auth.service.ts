import {HttpClient, HttpHeaders} from '@angular/common/http';
import {Injectable, Inject, PLATFORM_ID} from '@angular/core';
import {Router} from '@angular/router';
import { JwtHelperService } from '@auth0/angular-jwt';
import { isPlatformBrowser } from '@angular/common';

import {BehaviorSubject, Observable} from 'rxjs';
import {finalize, first, tap} from 'rxjs/operators';
import { UserRes } from '../../model/response/user-res.model';
import { UserReq } from '../../model/request/user-req.model';
import { Role } from '../../constant/role';
import { ConfigService } from './config.service';
import { AuthUtilsService } from './auth-utils.service';

@Injectable({providedIn: 'root'})
export class AuthService {



  public readonly _authSubject$: BehaviorSubject<UserRes | undefined>;
  public readonly _auth$: Observable<UserRes | undefined>;
  private _loading$ = new BehaviorSubject<boolean>(false);

  constructor(
    private router: Router,
    private http: HttpClient,
    private jwtHelper: JwtHelperService,
    private authUtilsService: AuthUtilsService,
    @Inject(PLATFORM_ID) private platformId: Object,
    private configService: ConfigService
  ) {
    // Initialize with undefined or get from localStorage if in browser using AuthUtilsService
    let storedUser: UserRes | undefined = undefined;

    if (isPlatformBrowser(this.platformId)) {
      storedUser = this.authUtilsService.getUserFromStorage() || undefined;
    }

    this._authSubject$ = new BehaviorSubject<UserRes | undefined>(storedUser);
    this._auth$ = this._authSubject$.asObservable();
  }

  public get authValue(): UserRes | undefined {
    return this._authSubject$.getValue();
  }
  public isAdmin(): boolean {
    const roles = this.getRoles();
    if (!roles) return false;
    return roles.every(r => [Role.PANEL].includes(r));
  }

  public getRoles(): string[] | undefined {
    const user = this.authValue;
    if (user) {
      const tokenPayload = this.jwtHelper.decodeToken(user.tokens.access_token);
      return tokenPayload.roles;
    }
    return undefined;
  }

  public isAuthenticated(): boolean {
    return this.authUtilsService.isAuthenticated();
  }

  public isTokenExpired(): boolean {
    return this.authUtilsService.isTokenExpired();
  }
  register(authReq: UserReq): Observable<UserRes> {
    this.loading$.next(true);
    return this.http
      .post<UserRes>(
        `${this.configService.apiUrl}/users/register`,
        authReq
      )
      .pipe(
        first(),
        tap(user => this.updateUser(user)),
        finalize(() => this.loading$.next(false))
      );
  }

  updateUser(user: UserRes) {
    // store user details and jwt token in local storage to keep user logged in between page refreshes
    this._authSubject$.next(user);
    console.log('AuthService - updateUser called, user:', user?.id);

    if (isPlatformBrowser(this.platformId)) {
      try {
        localStorage.setItem('user', JSON.stringify(user));
        console.log('AuthService - User saved to localStorage');
      } catch (error) {
        console.error('AuthService - Error saving user to localStorage:', error);
      }
    }
  }

  refreshToken(refreshToken: string): Observable<UserRes> {
    this.loading$.next(true);
    const headers = new HttpHeaders({'x-refresh-token': refreshToken});

    return this.http
      .post<UserRes>(
        `${this.configService.apiUrl}/access/refresh`, null, {headers}
      )
      .pipe(
        first(),
        tap(user => this.updateUser(user)),
        finalize(() => this.loading$.next(false)));
  }



  login(userName: string | null, password: string | null): Observable<UserRes> {
    console.log('AuthService - login method called');
    this.loading$.next(true);
    return this.http
      .post<UserRes>(
        `${this.configService.apiUrl}/access/login`,
        {
          user_name: userName,
          password
        }
      )
      .pipe(
        first(),
        tap(user => {
          console.log('AuthService - Login successful, updating user');
          this.updateUser(user);
        }),
        finalize(() => {
          console.log('AuthService - Login finalized');
          this.loading$.next(false);
        })
      );
  }

  logout() {
    this.loading$.next(true);
    this.http
      .post<UserRes>(
        `${this.configService.apiUrl}/access/logout`, null
      )
      .pipe(
        first(),
        finalize(() => {
          this.loading$.next(false);
          this._authSubject$.next(undefined);

          // Clear all authentication data using AuthUtilsService
          this.authUtilsService.clearAuthData();

          this.redirectTo('/auth/login');
        })).subscribe();

  }

  redirectTo(uri: string) {
    this.router.navigate([uri]).then(() => {
    // Reload toàn bộ trang
    window.location.reload();
  });
  }

  get loading$(): BehaviorSubject<boolean> {
    return this._loading$;
  }

  get auth$(): Observable<UserRes | undefined> {
    return this._auth$;
  }
}
