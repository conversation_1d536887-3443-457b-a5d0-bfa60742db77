    # Admin panel routing - MUST BE BEFORE FRONTEND ROUTING
    location /admin-2/ {
        rewrite ^/admin-2/(.*)$ /panel/$1 break;
        proxy_pass http://smm-admin:4001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_set_header X-Tenant-ID "$tenant_id";
        proxy_set_header X-Tenant-Domain "$tenant_domain";
        proxy_set_header X-Tenant-API-URL "$tenant_api_url";

        # Only apply sub_filter to HTML files
        sub_filter '</head>' '<script>
            window.TENANT_CONFIG = {
                tenantId: "fa4bb96f-38ea-47ff-a176-f83d348be34f",
                domain: "childautovnfb.click",
                apiUrl: "https://childautovnfb.click/api/v1"
            };
        </script></head>';
        sub_filter_once on;
        sub_filter_types text/html;
    }

    # Frontend routing
    location / {
