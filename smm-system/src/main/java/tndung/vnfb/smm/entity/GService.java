package tndung.vnfb.smm.entity;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.Filter;
import org.hibernate.annotations.FilterDef;
import org.hibernate.annotations.ParamDef;
import tndung.vnfb.smm.constant.Common;
import tndung.vnfb.smm.constant.enums.*;
import tndung.vnfb.smm.dto.ServiceLabel;
import tndung.vnfb.smm.entity.audit.AbstractAuditEntity;
import tndung.vnfb.smm.entity.audit.AbstractTenantEntity;
import tndung.vnfb.smm.entity.convert.ServiceLabelConverter;

import javax.persistence.*;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;

@Entity
@Getter
@Setter
@NoArgsConstructor
@Table(name = "g_service")
@FilterDef(name = "tenantFilter", parameters = { @ParamDef(name = "tenantId", type = "string") ,  @ParamDef(name = "wildcardTenant", type = "string") })
@Filter(name = "tenantFilter", condition = "(tenant_id = :tenantId  OR tenant_id = :wildcardTenant)")
public class GService extends AbstractTenantEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne
    @JoinColumn(name = "category_id", referencedColumnName = "id")
    private Category category;

    private String name;
    private String description;
    private BigDecimal price;
//    private BigDecimal price1;
//    private BigDecimal price2;

    private BigDecimal percent;
//    private BigDecimal percent1;
//    private BigDecimal percent2;

    private Long averageTime; // in seconds


    private Boolean isFixedPrice = Boolean.FALSE;
    private Boolean refill;

    private Integer refillDays;
    private BigDecimal originalPrice;
    private Long min;
    private Long max;
    private String apiServiceId;



    @OneToOne
    @JoinColumn(name = "api_provider_id", referencedColumnName = "id")
    private ApiProvider apiProvider;


    private Boolean dripFeed;

    @Column
    @Enumerated(EnumType.STRING)
    private AddType addType;


    @Column
    @Convert(converter = ServiceLabelConverter.class)
    private List<ServiceLabel> labels = new ArrayList<>();

    @Column
    @Enumerated(EnumType.STRING)
    private ServiceType type;


    private Integer sort;
    private Boolean autoSync;
    private Boolean syncMinMax;
    private Boolean syncRefill;
    private Boolean syncCancel;
    private Boolean syncStatus;

    private Integer limitFrom;
    private Integer limitTo;

    private String sampleLink;
    private Boolean isOverflow;
    private Integer overflow;

    private Integer speedPerDay;

    private Boolean cancelButton;

    // User-friendly description fields
    private String startTimeContent;
    private Boolean startTimeEnabled = Boolean.FALSE;
    private String warrantyContent;
    private Boolean warrantyEnabled = Boolean.FALSE;
    private String speedContent;
    private Boolean speedEnabled = Boolean.FALSE;

    @Column(columnDefinition = "numberic(1)")
    @Enumerated(EnumType.ORDINAL)
    private CommonStatus status = CommonStatus.ACTIVATED;
    private Boolean isDeleted = Boolean.FALSE;

    @OneToMany
    @JoinColumn(name = "service_id", referencedColumnName = "id")
    private List<SpecialPrice> specialPrices;

    /**
     * Helper method to apply currency rate conversion to a price
     * @param price The price to convert
     * @return The converted price, or null if input price is null
     */
    private BigDecimal applyExchangeRate(BigDecimal price) {
        if (price == null) {
            return null;
        }

        BigDecimal rate = BigDecimal.ONE; // Default rate
        if (apiProvider != null && apiProvider.getCurrencyRate() != null) {
            rate = apiProvider.getCurrencyRate();
        }

        return price.divide(rate, Common.SCALE_DEFAULT, RoundingMode.HALF_UP);
    }

    /**
     * Override getter for originalPrice to apply currency rate conversion
     * If provider or currency rate is null, default rate of 1 is used
     */
    public BigDecimal getOriginalPrice() {
        return applyExchangeRate(originalPrice);
    }

    /**
     * Override getter for price to apply currency rate conversion
     * If provider or currency rate is null, default rate of 1 is used
     */
    public BigDecimal getPrice() {
        return applyExchangeRate(price);
    }

}
