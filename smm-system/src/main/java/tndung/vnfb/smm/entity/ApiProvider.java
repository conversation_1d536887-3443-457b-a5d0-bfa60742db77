package tndung.vnfb.smm.entity;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.Filter;
import org.hibernate.annotations.FilterDef;
import org.hibernate.annotations.ParamDef;
import org.hibernate.annotations.Where;
import tndung.vnfb.smm.constant.enums.CommonStatus;
import tndung.vnfb.smm.constant.enums.Currency;
import tndung.vnfb.smm.entity.audit.AbstractAuditEntity;
import tndung.vnfb.smm.entity.audit.AbstractTenantEntity;

import javax.persistence.*;
import java.math.BigDecimal;
import java.time.OffsetDateTime;

@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@FilterDef(name = "tenantFilter", parameters = { @ParamDef(name = "tenantId", type = "string") ,  @ParamDef(name = "wildcardTenant", type = "string") })
@Filter(name = "tenantFilter", condition = "(tenant_id = :tenantId  OR tenant_id = :wildcardTenant)")
public class ApiProvider extends AbstractTenantEntity {

    public ApiProvider(Long id) {
        this.id = id;
    }

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(columnDefinition = "VARCHAR(200)")
    private String url;

    @Column(columnDefinition = "VARCHAR(200)")
    private String secretKey;

    @Column(columnDefinition = "DOUBLE(10, 4)")
    private BigDecimal balance;

    @Column(columnDefinition = "DOUBLE(10, 4)")
    private BigDecimal balanceAlert = BigDecimal.ZERO;

    @Column(columnDefinition = "DOUBLE(10, 4)")
    private BigDecimal previewBalance = BigDecimal.ZERO;

    @Column(columnDefinition = "DOUBLE(10, 4)")
    private BigDecimal currencyRate = BigDecimal.ONE;

    @Column(columnDefinition = "BOOLEAN")
    private Boolean enableRate = false;

    @Column(columnDefinition = "VARCHAR(4)")
    private String currency;

    @Column(columnDefinition = "VARCHAR(200)")
    private String description;

    @Column(columnDefinition = "VARCHAR(200)")
    private String name;

    private Boolean isDeleted = false;

    @Column(columnDefinition = "numberic(1)")
    @Enumerated(EnumType.ORDINAL)
    private CommonStatus status = CommonStatus.ACTIVATED;

    @Column(name = "last_noti_at")
    private OffsetDateTime lastNotiAt;

    /**
     * Check if notification was sent within last 24 hours
     */
    public boolean wasSentInLast24Hours() {
        if (lastNotiAt == null) {
            return false;
        }
        return lastNotiAt.isAfter(OffsetDateTime.now().minusHours(24));
    }

    /**
     * Update last notification timestamp to current time
     */
    public void updateLastNotiAt() {
        this.lastNotiAt = OffsetDateTime.now();
    }

}
