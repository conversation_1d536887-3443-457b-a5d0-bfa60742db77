package tndung.vnfb.smm.cron;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import tndung.vnfb.smm.service.AverageTimeCalculationService;

/**
 * Scheduled job for calculating average time for services
 * Runs every 20 minutes
 */
@Component
@RequiredArgsConstructor
@Slf4j
@Profile("!local")
public class AverageTimeCalculationJob {

    private final AverageTimeCalculationService averageTimeCalculationService;

    /**
     * Main calculation job - runs every 20 minutes
     * Processes all due calculations and early calculations
     */
    @Scheduled(fixedRate = 20 * 60 * 1000) // 20 minutes in milliseconds
    public void calculateAverageTimes() {
        log.info("Starting average time calculation job");
        
        try {
            // First, ensure all enabled services have calculation runs
            averageTimeCalculationService.initializeCalculationRunsForEnabledServices();
            
            // Process all due calculations
            averageTimeCalculationService.processAllDueCalculations();
            
            // Process early calculations (during buffer time)
            averageTimeCalculationService.processEarlyCalculations();
            
            log.info("Average time calculation job completed successfully");
            
        } catch (Exception e) {
            log.error("Error in average time calculation job: {}", e.getMessage(), e);
        }
    }

    /**
     * Cleanup job - runs daily at 2 AM
     * Removes stale calculation runs
     */
    @Scheduled(cron = "0 0 2 * * ?")
    public void cleanupStaleCalculationRuns() {
        log.info("Starting cleanup of stale calculation runs");
        
        try {
            averageTimeCalculationService.cleanupStaleCalculationRuns();
            log.info("Cleanup of stale calculation runs completed successfully");
            
        } catch (Exception e) {
            log.error("Error in cleanup job: {}", e.getMessage(), e);
        }
    }

    /**
     * Initialization job - runs every hour to check for new services
     * Ensures new enabled services get calculation runs
     */
    @Scheduled(fixedRate = 60 * 60 * 1000) // 1 hour in milliseconds
    public void initializeNewServices() {
        log.info("Starting initialization check for new services");
        
        try {
            averageTimeCalculationService.initializeCalculationRunsForEnabledServices();
            log.info("New services initialization check completed successfully");
            
        } catch (Exception e) {
            log.error("Error in new services initialization: {}", e.getMessage(), e);
        }
    }
}
