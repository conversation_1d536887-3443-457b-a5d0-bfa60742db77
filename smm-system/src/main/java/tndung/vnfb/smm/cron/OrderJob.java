package tndung.vnfb.smm.cron;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import tndung.vnfb.smm.config.TenantContext;
import tndung.vnfb.smm.service.SMMJobService;

import static tndung.vnfb.smm.constant.Common.FULL_ACCESS_TENANT;

@Slf4j
@RequiredArgsConstructor
@Component
@Profile("!local") // Disable in local environment
public class OrderJob {

    private final SMMJobService SMMJobService;

    @Scheduled(fixedDelay = 1500 * 60)
    public void triggerRunUpdateOrderStatus() {
        log.info("Triggered handle run job order status");

        SMMJobService.triggerRun();


    }
}
