package tndung.vnfb.smm.dto.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import tndung.vnfb.smm.constant.enums.CommonStatus;
import tndung.vnfb.smm.constant.enums.Currency;

import java.math.BigDecimal;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ApiProviderRes {


    private Long id;

    private String name;
    private String url;

    private BigDecimal balanceAlert;

    private BigDecimal balance;

    private BigDecimal previewBalance;

    private BigDecimal currencyRate;

    private Boolean enableRate;

    private Currency currency;

    private CommonStatus status;
}
