-- Migration script to add enable_rate column to api_provider table
-- Date: 2025-07-15
-- Description: Add enable_rate boolean column to track currency rate toggle status

-- Add enable_rate column to api_provider table
ALTER TABLE api_provider 
ADD COLUMN enable_rate BOOLEAN DEFAULT FALSE;

-- Update existing records to set enable_rate based on currency_rate
-- If currency_rate is not 1, set enable_rate to true
UPDATE api_provider 
SET enable_rate = CASE 
    WHEN currency_rate IS NOT NULL AND currency_rate != 1 THEN TRUE 
    ELSE FALSE 
END;

-- Add comment to the column for documentation
COMMENT ON COLUMN api_provider.enable_rate IS 'Boolean flag to indicate if currency rate is enabled for this provider';
